#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的消息记录添加脚本
用于快速向飞书多维表格添加消息记录
"""

from feishu_api_curl import FeishuBitableAPICurl
from datetime import datetime
import sys

# 配置信息
APP_ID = "cli_a828491ea031d013"
APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
APP_TOKEN = "TtULb7pBiaGRMgs4dfac4aLAnId"
TABLE_ID = "tblzpzFoRcDR3PC3"  # "数据表"的ID

def add_message(sender: str, content: str, message_time: datetime = None):
    """
    添加消息记录
    
    Args:
        sender: 发送人
        content: 消息内容
        message_time: 消息时间，默认为当前时间
    """
    if message_time is None:
        message_time = datetime.now()
    
    # 初始化API客户端
    api = FeishuBitableAPICurl(APP_ID, APP_SECRET)
    
    # 准备记录数据
    fields = {
        "消息时间": int(message_time.timestamp() * 1000),  # 毫秒时间戳
        "发送人": sender,
        "消息内容": content
        # 注意: 消息附件字段需要先上传文件获取file_token，这里暂时不添加
    }
    
    try:
        # 新增记录
        result = api.add_record(APP_TOKEN, TABLE_ID, fields)
        
        print(f"✅ 消息记录添加成功！")
        print(f"记录ID: {result.get('record_id')}")
        print(f"发送人: {sender}")
        print(f"消息内容: {content}")
        print(f"消息时间: {message_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return result
        
    except Exception as e:
        print(f"❌ 添加记录失败: {str(e)}")
        return None

def add_multiple_messages(messages: list):
    """
    批量添加消息记录
    
    Args:
        messages: 消息列表，每个消息格式为 {"sender": "发送人", "content": "内容", "time": datetime对象(可选)}
    """
    # 初始化API客户端
    api = FeishuBitableAPICurl(APP_ID, APP_SECRET)
    
    # 准备批量记录数据
    records = []
    for msg in messages:
        msg_time = msg.get('time', datetime.now())
        fields = {
            "消息时间": int(msg_time.timestamp() * 1000),
            "发送人": msg['sender'],
            "消息内容": msg['content']
        }
        records.append({"fields": fields})
    
    try:
        # 批量新增记录
        results = api.add_records_batch(APP_TOKEN, TABLE_ID, records)
        
        print(f"✅ 批量添加成功，共 {len(results)} 条记录！")
        for i, result in enumerate(results):
            print(f"  记录{i+1}: {result.get('record_id')}")
        
        return results
        
    except Exception as e:
        print(f"❌ 批量添加失败: {str(e)}")
        return None

def main():
    """主函数 - 命令行接口"""
    if len(sys.argv) < 3:
        print("使用方法:")
        print("  python add_message_record.py <发送人> <消息内容>")
        print("  例如: python add_message_record.py '张三' '这是一条测试消息'")
        print()
        print("或者直接运行脚本进行交互式添加:")
        print("  python add_message_record.py")
        print()
        
        # 交互式模式
        print("=== 交互式添加消息记录 ===")
        
        while True:
            try:
                sender = input("请输入发送人 (输入 'quit' 退出): ").strip()
                if sender.lower() == 'quit':
                    break
                
                if not sender:
                    print("发送人不能为空，请重新输入")
                    continue
                
                content = input("请输入消息内容: ").strip()
                if not content:
                    print("消息内容不能为空，请重新输入")
                    continue
                
                # 添加记录
                result = add_message(sender, content)
                
                if result:
                    print()
                    continue_input = input("是否继续添加？(y/n): ").strip().lower()
                    if continue_input != 'y':
                        break
                else:
                    print("添加失败，请检查网络连接和配置")
                    break
                    
                print()
                
            except KeyboardInterrupt:
                print("\n\n程序已退出")
                break
            except Exception as e:
                print(f"发生错误: {str(e)}")
                break
    
    else:
        # 命令行模式
        sender = sys.argv[1]
        content = sys.argv[2]
        
        print(f"添加消息记录:")
        print(f"发送人: {sender}")
        print(f"消息内容: {content}")
        print()
        
        result = add_message(sender, content)
        
        if not result:
            sys.exit(1)

def demo():
    """演示函数"""
    print("=== 演示添加消息记录 ===")
    
    # 单条记录示例
    print("1. 添加单条记录:")
    add_message("演示用户", "这是一条演示消息")
    
    print("\n2. 批量添加记录:")
    messages = [
        {"sender": "用户A", "content": "消息1"},
        {"sender": "用户B", "content": "消息2"},
        {"sender": "用户C", "content": "消息3"}
    ]
    add_multiple_messages(messages)

if __name__ == "__main__":
    # 如果参数中包含 'demo'，运行演示
    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        demo()
    else:
        main()
