#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书多维表格API使用示例
演示如何新增记录到您的多维表格
"""

from feishu_bitable_api import FeishuBitableAPI
from datetime import datetime
import time

def main():
    """使用示例"""
    
    # 您的配置信息
    APP_ID = "cli_a828491ea031d013"
    APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
    BITABLE_URL = "https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId"
    
    print("=== 飞书多维表格API测试 ===")
    print(f"应用ID: {APP_ID}")
    print(f"表格链接: {BITABLE_URL}")
    print()
    
    try:
        # 1. 初始化API客户端
        print("1. 初始化API客户端...")
        api = FeishuBitableAPI(APP_ID, APP_SECRET)
        
        # 2. 获取访问令牌
        print("2. 获取访问令牌...")
        token = api.get_tenant_access_token()
        print(f"   令牌获取成功: {token[:20]}...")
        
        # 3. 解析表格URL
        print("3. 解析表格URL...")
        app_token, _ = api.extract_app_token_and_table_id(BITABLE_URL)
        print(f"   app_token: {app_token}")
        
        # 4. 获取数据表列表
        print("4. 获取数据表列表...")
        tables = api.get_tables(app_token)
        
        if not tables:
            print("   错误: 未找到任何数据表")
            return
        
        # 使用第一个表格
        table_id = tables[0]['table_id']
        table_name = tables[0]['name']
        print(f"   将使用表格: {table_name}")
        print(f"   table_id: {table_id}")
        
        # 5. 新增测试记录
        print("\n5. 新增测试记录...")
        
        # 准备记录数据
        current_time = int(datetime.now().timestamp() * 1000)  # 毫秒时间戳
        
        test_record = {
            "消息时间": current_time,
            "发送人": "API测试用户",
            "消息内容": f"这是通过API新增的测试消息 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            # 注意: 消息附件字段需要先上传文件获取file_token，这里暂时不添加
        }
        
        print(f"   记录数据: {test_record}")
        
        # 新增记录
        result = api.add_record(app_token, table_id, test_record)
        
        print(f"   ✅ 记录新增成功!")
        print(f"   记录ID: {result.get('record_id')}")
        
        # 6. 批量新增示例（可选）
        print("\n6. 批量新增示例...")
        
        batch_records = []
        for i in range(3):
            fields = {
                "消息时间": int(datetime.now().timestamp() * 1000),
                "发送人": f"批量用户{i+1}",
                "消息内容": f"批量消息 {i+1} - {datetime.now().strftime('%H:%M:%S')}"
            }
            batch_records.append({"fields": fields})
            time.sleep(0.1)  # 稍微延迟以区分时间
        
        batch_result = api.add_records_batch(app_token, table_id, batch_records)
        print(f"   ✅ 批量新增成功，共 {len(batch_result)} 条记录")
        
        print("\n=== 测试完成 ===")
        print("请检查您的飞书多维表格，应该可以看到新增的记录。")
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        print("\n可能的原因:")
        print("1. 应用权限不足，请确保应用已添加到多维表格中")
        print("2. app_id 或 app_secret 不正确")
        print("3. 多维表格链接无效或无权访问")
        print("4. 字段名称不匹配，请检查表格中的实际字段名")


if __name__ == "__main__":
    main()
