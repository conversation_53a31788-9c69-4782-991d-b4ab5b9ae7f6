# 飞书多维表格API - AI使用指令

## 任务目标
通过飞书开放平台API向多维表格新增记录

## 关键参数
```
APP_ID = "cli_a828491ea031d013"
APP_SECRET = "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
APP_TOKEN = "TtULb7pBiaGRMgs4dfac4aLAnId"
TABLE_ID = "tblzpzFoRcDR3PC3"  # "数据表"
BASE_URL = "https://open.feishu.cn/open-apis"
```

## 表格字段
- **消息时间**: 日期类型，使用毫秒时间戳
- **发送人**: 文本类型
- **消息内容**: 文本类型
- **消息附件**: 附件类型（可选）

## 核心代码模板

```python
import subprocess
import json
from datetime import datetime

def execute_curl(method, url, headers=None, data=None):
    """执行curl命令 - 推荐方案，避免Python网络问题"""
    cmd = ['curl', '-X', method, url, '--insecure', '--silent']
    
    if headers:
        for k, v in headers.items():
            cmd.extend(['-H', f'{k}: {v}'])
    if data:
        cmd.extend(['-d', data])
    
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
    return json.loads(result.stdout)

def get_access_token():
    """获取访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = json.dumps({
        "app_id": "cli_a828491ea031d013",
        "app_secret": "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT"
    })
    headers = {'Content-Type': 'application/json'}
    
    result = execute_curl('POST', url, headers, data)
    return result['tenant_access_token']

def add_record(sender, content, message_time=None):
    """新增记录到飞书多维表格"""
    if message_time is None:
        message_time = datetime.now()
    
    # 获取访问令牌
    token = get_access_token()
    
    # 准备数据
    fields = {
        "消息时间": int(message_time.timestamp() * 1000),  # 毫秒时间戳
        "发送人": sender,
        "消息内容": content
    }
    
    # API调用
    url = "https://open.feishu.cn/open-apis/bitable/v1/apps/TtULb7pBiaGRMgs4dfac4aLAnId/tables/tblzpzFoRcDR3PC3/records"
    data = json.dumps({"fields": fields})
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('POST', url, headers, data)
    return result['data']['record']['record_id']

# 使用示例
record_id = add_record("张三", "这是一条测试消息")
print(f"新增记录ID: {record_id}")
```

## 批量新增模板

```python
def add_multiple_records(messages):
    """批量新增记录
    messages格式: [{"sender": "用户", "content": "内容", "time": datetime对象(可选)}]
    """
    token = get_access_token()
    
    records = []
    for msg in messages:
        msg_time = msg.get('time', datetime.now())
        fields = {
            "消息时间": int(msg_time.timestamp() * 1000),
            "发送人": msg['sender'],
            "消息内容": msg['content']
        }
        records.append({"fields": fields})
    
    url = "https://open.feishu.cn/open-apis/bitable/v1/apps/TtULb7pBiaGRMgs4dfac4aLAnId/tables/tblzpzFoRcDR3PC3/records/batch_create"
    data = json.dumps({"records": records})
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    result = execute_curl('POST', url, headers, data)
    return [r['record_id'] for r in result['data']['records']]

# 批量使用示例
messages = [
    {"sender": "用户1", "content": "消息1"},
    {"sender": "用户2", "content": "消息2"}
]
record_ids = add_multiple_records(messages)
print(f"批量新增记录IDs: {record_ids}")
```

## 重要注意事项

1. **网络方案**: 优先使用curl，避免Python requests的SSL/代理问题
2. **时间格式**: 日期字段必须使用毫秒时间戳（13位数字）
3. **字段名称**: 必须与表格中的字段名完全匹配
4. **错误处理**: 添加try-catch处理网络和API错误
5. **令牌缓存**: 访问令牌有效期约2小时，可以缓存使用

## 快速测试

```python
def test_api():
    """快速测试API连接"""
    try:
        # 测试获取令牌
        token = get_access_token()
        print(f"✅ 令牌获取成功: {token[:20]}...")
        
        # 测试新增记录
        record_id = add_record("测试用户", "API连接测试")
        print(f"✅ 记录新增成功: {record_id}")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

# 运行测试
test_api()
```

## 常见问题解决

1. **SSL错误**: 使用curl + `--insecure`参数
2. **权限错误**: 检查应用是否添加到表格
3. **字段错误**: 确认字段名称完全匹配
4. **格式错误**: 检查时间戳格式（毫秒）

## 扩展功能提示

基于此基础，可以扩展：
- 查询记录: 修改URL为 `/records/search`，使用GET方法
- 更新记录: 修改URL为 `/records/{record_id}`，使用PUT方法
- 删除记录: 修改URL为 `/records/{record_id}`，使用DELETE方法

---

**使用说明**: 复制上述代码模板，根据具体需求修改sender和content参数即可快速实现飞书多维表格记录新增功能。
