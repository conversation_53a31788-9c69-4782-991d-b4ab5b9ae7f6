# 飞书多维表格API - 快速参考

## 核心信息

### API基础
- **基础URL**: `https://open.feishu.cn/open-apis`
- **认证方式**: Bearer Token
- **内容类型**: `application/json`

### 必需参数
- `app_id`: 飞书应用ID
- `app_secret`: 飞书应用密钥
- `app_token`: 从多维表格URL提取 (`/base/{app_token}`)
- `table_id`: 通过API获取数据表列表得到

## 4步操作流程

### 1. 获取访问令牌
```bash
curl -X POST "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal" \
  -H "Content-Type: application/json" \
  -d '{"app_id":"your_app_id","app_secret":"your_app_secret"}' \
  --insecure --silent
```

### 2. 获取数据表列表
```bash
curl -X GET "https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  --insecure --silent
```

### 3. 新增单条记录
```bash
curl -X POST "https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{"fields":{"字段名":"字段值"}}' \
  --insecure --silent
```

### 4. 批量新增记录
```bash
curl -X POST "https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{"records":[{"fields":{"字段名":"字段值"}}]}' \
  --insecure --silent
```

## Python实现模板

```python
import subprocess
import json
from datetime import datetime

class FeishuAPI:
    def __init__(self, app_id, app_secret):
        self.app_id = app_id
        self.app_secret = app_secret
        self.base_url = "https://open.feishu.cn/open-apis"
    
    def _curl(self, method, url, headers=None, data=None):
        cmd = ['curl', '-X', method, url, '--insecure', '--silent']
        if headers:
            for k, v in headers.items():
                cmd.extend(['-H', f'{k}: {v}'])
        if data:
            cmd.extend(['-d', data])
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        return json.loads(result.stdout)
    
    def get_token(self):
        url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        data = json.dumps({"app_id": self.app_id, "app_secret": self.app_secret})
        headers = {'Content-Type': 'application/json'}
        
        result = self._curl('POST', url, headers, data)
        return result['tenant_access_token']
    
    def get_tables(self, app_token, access_token):
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        result = self._curl('GET', url, headers)
        return result['data']['items']
    
    def add_record(self, app_token, table_id, fields, access_token):
        url = f"{self.base_url}/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        data = json.dumps({"fields": fields})
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        result = self._curl('POST', url, headers, data)
        return result['data']['record']

# 使用示例
api = FeishuAPI("your_app_id", "your_app_secret")
token = api.get_token()

fields = {
    "消息时间": int(datetime.now().timestamp() * 1000),
    "发送人": "用户名",
    "消息内容": "消息内容"
}

record = api.add_record("app_token", "table_id", fields, token)
print(f"记录ID: {record['record_id']}")
```

## 字段格式速查

| 类型 | 格式 | 示例 |
|------|------|------|
| 文本 | 字符串 | `"文本内容"` |
| 数字 | 数值 | `123` |
| 日期 | 毫秒时间戳 | `1703123456789` |
| 单选 | 字符串 | `"选项1"` |
| 多选 | 数组 | `["选项1", "选项2"]` |
| 复选框 | 布尔 | `true` |
| 附件 | 对象数组 | `[{"file_token": "xxx"}]` |
| 人员 | 对象数组 | `[{"id": "ou_xxx"}]` |
| 超链接 | 对象 | `{"text": "名称", "link": "url"}` |

## 时间戳转换

```python
from datetime import datetime

# 当前时间
timestamp = int(datetime.now().timestamp() * 1000)

# 指定时间
dt = datetime(2024, 1, 1, 12, 0, 0)
timestamp = int(dt.timestamp() * 1000)

# 时间戳转日期
dt = datetime.fromtimestamp(timestamp / 1000)
```

## 常见错误

| 错误码 | 原因 | 解决方案 |
|--------|------|----------|
| 99991663 | 权限不足 | 检查应用权限和表格权限 |
| 99991400 | 参数错误 | 检查字段名和数据格式 |
| 99991404 | 资源不存在 | 检查app_token和table_id |
| 99991429 | 频率限制 | 降低调用频率 |

## 网络问题解决

1. **SSL错误**: 使用 `--insecure` 参数
2. **代理问题**: 使用curl替代requests
3. **编码问题**: 设置 `encoding='utf-8'`
4. **超时问题**: 增加 `timeout` 参数

## 完整示例

```python
# 完整的新增记录示例
def add_message_record(app_id, app_secret, app_token, table_id, sender, content):
    api = FeishuAPI(app_id, app_secret)
    
    # 获取令牌
    token = api.get_token()
    
    # 准备数据
    fields = {
        "消息时间": int(datetime.now().timestamp() * 1000),
        "发送人": sender,
        "消息内容": content
    }
    
    # 新增记录
    record = api.add_record(app_token, table_id, fields, token)
    return record['record_id']

# 调用示例
record_id = add_message_record(
    "cli_a828491ea031d013",
    "eJsXnjJJVZh9K1XTnR7JbgxBzGbDMlRT", 
    "TtULb7pBiaGRMgs4dfac4aLAnId",
    "tblzpzFoRcDR3PC3",
    "张三",
    "这是一条测试消息"
)
print(f"新增记录ID: {record_id}")
```

## 调试技巧

1. **测试连接**: 先测试获取令牌接口
2. **验证权限**: 检查应用是否添加到表格
3. **确认字段**: 先获取表格结构确认字段名
4. **逐步调试**: 从简单字段开始，逐步增加复杂字段
5. **查看响应**: 打印完整的API响应进行调试

---

**提示**: 将此文档提供给其他AI时，确保同时提供具体的app_id、app_secret、app_token和table_id参数。
